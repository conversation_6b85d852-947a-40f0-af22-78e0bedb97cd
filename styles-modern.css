/* Modern WordCamp Design - Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Primary Colors */
    --primary-blue: #1e3a8a;
    --primary-purple: #7c3aed;
    --primary-gradient: linear-gradient(135deg, #1e3a8a 0%, #7c3aed 100%);
    
    /* Accent Colors */
    --accent-orange: #f97316;
    --accent-pink: #ec4899;
    --accent-green: #10b981;
    --accent-yellow: #fbbf24;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Typography */
    --font-primary: 'Poppins', sans-serif;
    --font-secondary: 'Inter', sans-serif;
    
    /* Spacing */
    --container-max-width: 1200px;
    --section-padding: 100px 0;
    --border-radius: 16px;
    --border-radius-lg: 24px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

body {
    font-family: var(--font-secondary);
    line-height: 1.6;
    color: var(--gray-700);
    overflow-x: hidden;
}

.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.logo-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 24px;
    color: var(--primary-blue);
}

.logo-wrapper i {
    font-size: 32px;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-year {
    color: var(--accent-orange);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 40px;
    margin: 0;
}

.nav-link {
    text-decoration: none;
    color: var(--gray-700);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-blue);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--gray-700);
    transition: all 0.3s ease;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    font-family: var(--font-secondary);
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
}

.btn-outline:hover {
    background: var(--primary-blue);
    color: var(--white);
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.1;
}

.shape-1 {
    width: 400px;
    height: 400px;
    background: var(--primary-gradient);
    top: -200px;
    right: -200px;
    animation: float 6s ease-in-out infinite;
}

.shape-2 {
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, var(--accent-pink), var(--accent-orange));
    bottom: -150px;
    left: -150px;
    animation: float 8s ease-in-out infinite reverse;
}

.shape-3 {
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, var(--accent-green), var(--accent-yellow));
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: float 10s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-family: var(--font-primary);
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 24px;
}

.title-line {
    display: block;
}

.highlight {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    color: var(--gray-600);
    margin-bottom: 40px;
    line-height: 1.6;
}

.hero-actions {
    display: flex;
    gap: 20px;
    margin-bottom: 60px;
}

.hero-stats {
    display: flex;
    gap: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--font-primary);
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-blue);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.hero-image {
    position: relative;
}

.image-wrapper {
    position: relative;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.hero-img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    display: block;
}

.image-decoration {
    position: absolute;
    top: -20px;
    right: -20px;
    width: 100px;
    height: 100px;
}

.decoration-circle {
    width: 80px;
    height: 80px;
    border: 3px solid var(--accent-orange);
    border-radius: 50%;
    position: absolute;
    top: 0;
    right: 0;
}

.decoration-dots {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 20px;
    height: 20px;
    background: var(--accent-pink);
    border-radius: 50%;
}

.decoration-dots::before,
.decoration-dots::after {
    content: '';
    position: absolute;
    width: 12px;
    height: 12px;
    background: var(--accent-green);
    border-radius: 50%;
}

.decoration-dots::before {
    top: -15px;
    left: -15px;
}

.decoration-dots::after {
    bottom: -15px;
    right: -15px;
    background: var(--accent-yellow);
}

/* Event Info Cards */
.event-info {
    padding: 80px 0;
    background: var(--white);
    margin-top: -40px;
    position: relative;
    z-index: 3;
}

.info-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.info-card {
    background: var(--white);
    padding: 40px 30px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    text-align: center;
    transition: transform 0.3s ease;
}

.info-card:hover {
    transform: translateY(-10px);
}

.card-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 24px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: var(--white);
}

.info-card h3 {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--gray-800);
}

.info-card p {
    color: var(--gray-600);
    margin-bottom: 24px;
    line-height: 1.6;
}

.card-link {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.card-link:hover {
    color: var(--primary-purple);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-title {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 20px;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* About Section */
.about {
    padding: var(--section-padding);
    background: var(--gray-50);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.about-text h3 {
    font-family: var(--font-primary);
    font-size: 2rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 24px;
    line-height: 1.3;
}

.about-text p {
    font-size: 1.125rem;
    color: var(--gray-600);
    margin-bottom: 32px;
    line-height: 1.6;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
    color: var(--gray-700);
}

.feature-item i {
    color: var(--accent-green);
    font-size: 20px;
}

.about-img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

/* Speakers Section */
.speakers {
    padding: var(--section-padding);
    background: var(--white);
}

.speakers-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
}

.speaker-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.speaker-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.speaker-image {
    position: relative;
    overflow: hidden;
}

.speaker-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.speaker-card:hover .speaker-image img {
    transform: scale(1.1);
}

.speaker-social {
    position: absolute;
    top: 16px;
    right: 16px;
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.speaker-card:hover .speaker-social {
    opacity: 1;
}

.speaker-social a {
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-blue);
    text-decoration: none;
    transition: all 0.3s ease;
}

.speaker-social a:hover {
    background: var(--primary-blue);
    color: var(--white);
}

.speaker-info {
    padding: 24px;
    text-align: center;
}

.speaker-name {
    font-family: var(--font-primary);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 8px;
}

.speaker-title {
    color: var(--primary-blue);
    font-weight: 500;
    margin-bottom: 4px;
}

.speaker-company {
    color: var(--gray-500);
    font-size: 0.875rem;
}

/* Highlights Section */
.highlights {
    padding: var(--section-padding);
    background: var(--gray-50);
}

.highlights-content {
    display: flex;
    flex-direction: column;
    gap: 80px;
}

.highlight-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.highlight-item:nth-child(even) {
    direction: rtl;
}

.highlight-item:nth-child(even) > * {
    direction: ltr;
}

.highlight-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

.highlight-text h3 {
    font-family: var(--font-primary);
    font-size: 2rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 20px;
}

.highlight-text p {
    font-size: 1.125rem;
    color: var(--gray-600);
    margin-bottom: 32px;
    line-height: 1.6;
}

/* Schedule Section */
.schedule {
    padding: var(--section-padding);
    background: var(--white);
}

.schedule-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 60px;
}

.tab-btn {
    padding: 12px 32px;
    background: transparent;
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-weight: 600;
    color: var(--gray-600);
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: var(--primary-gradient);
    border-color: transparent;
    color: var(--white);
}

.schedule-day {
    display: none;
}

.schedule-day.active {
    display: block;
}

.schedule-item {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 40px;
    padding: 30px 0;
    border-bottom: 1px solid var(--gray-200);
    align-items: center;
}

.schedule-item:last-child {
    border-bottom: none;
}

.schedule-time {
    font-family: var(--font-primary);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-blue);
    text-align: center;
    padding: 16px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.schedule-details h3 {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 8px;
}

.schedule-details p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Sponsors Section */
.sponsors {
    padding: var(--section-padding);
    background: var(--gray-50);
}

.sponsors-grid {
    margin-bottom: 80px;
}

.sponsor-tier {
    margin-bottom: 60px;
}

.tier-title {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
    text-align: center;
    margin-bottom: 40px;
}

.sponsor-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    flex-wrap: wrap;
}

.sponsor-logo {
    background: var(--white);
    padding: 20px 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: transform 0.3s ease;
}

.sponsor-logo:hover {
    transform: translateY(-5px);
}

.sponsor-logo img {
    height: 60px;
    width: auto;
    object-fit: contain;
}

.sponsor-cta {
    text-align: center;
    padding: 60px 40px;
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

.sponsor-cta h3 {
    font-family: var(--font-primary);
    font-size: 2rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 16px;
}

.sponsor-cta p {
    font-size: 1.125rem;
    color: var(--gray-600);
    margin-bottom: 32px;
}

/* Venue Section */
.venue {
    padding: var(--section-padding);
    background: var(--white);
}

.venue-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.venue-details h3 {
    font-family: var(--font-primary);
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 16px;
}

.venue-address {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.125rem;
    color: var(--gray-600);
    margin-bottom: 32px;
}

.venue-address i {
    color: var(--accent-orange);
    font-size: 20px;
}

.venue-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 40px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
    color: var(--gray-700);
}

.feature i {
    color: var(--primary-blue);
    font-size: 18px;
    width: 20px;
}

.venue-img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

/* Testimonials Section */
.testimonials {
    padding: var(--section-padding);
    background: var(--gray-50);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.testimonial-card {
    background: var(--white);
    padding: 40px 30px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    transition: transform 0.3s ease;
    position: relative;
}

.testimonial-card:hover {
    transform: translateY(-10px);
}

.testimonial-content {
    margin-bottom: 30px;
}

.quote-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    color: var(--white);
    font-size: 20px;
}

.testimonial-content p {
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--gray-700);
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 16px;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info h4 {
    font-family: var(--font-primary);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 4px;
}

.author-info p {
    color: var(--gray-500);
    font-size: 0.875rem;
}

/* Call to Action Section */
.cta-section {
    padding: var(--section-padding);
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-purple) 100%);
    color: var(--white);
    text-align: center;
}

.cta-content h2 {
    font-family: var(--font-primary);
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.cta-content > p {
    font-size: 1.25rem;
    margin-bottom: 60px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-stats {
    display: flex;
    justify-content: center;
    gap: 80px;
    margin-bottom: 60px;
}

.cta-stat {
    text-align: center;
}

.cta-stat .stat-number {
    display: block;
    font-family: var(--font-primary);
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.cta-stat .stat-label {
    font-size: 1rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.cta-actions {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-bottom: 40px;
}

.cta-actions .btn-outline {
    background: transparent;
    color: var(--white);
    border-color: var(--white);
}

.cta-actions .btn-outline:hover {
    background: var(--white);
    color: var(--primary-blue);
}

.cta-note {
    font-size: 0.875rem;
    opacity: 0.7;
    margin: 0;
}

/* Newsletter Section */
.newsletter {
    padding: 80px 0;
    background: var(--gray-900);
    color: var(--white);
}

.newsletter-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.newsletter h2 {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.newsletter p {
    font-size: 1.125rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    gap: 16px;
    max-width: 400px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 16px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    outline: none;
}

.newsletter-form .btn {
    white-space: nowrap;
    background: var(--primary-gradient);
    color: var(--white);
}

.newsletter-form .btn:hover {
    transform: translateY(-2px);
}

/* Footer */
.footer {
    background: var(--gray-900);
    color: var(--gray-300);
    padding: 80px 0 40px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 60px;
    margin-bottom: 60px;
}

.footer-section h3 {
    font-family: var(--font-primary);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 24px;
}

.footer-section p {
    line-height: 1.6;
    margin-bottom: 24px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 12px;
}

.footer-section ul li a {
    color: var(--gray-300);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--white);
}

.social-links {
    display: flex;
    gap: 16px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-300);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-blue);
    color: var(--white);
    transform: translateY(-2px);
}

.contact-info,
.event-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.contact-info p,
.event-details p {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 0;
}

.contact-info i,
.event-details i {
    color: var(--primary-blue);
    width: 16px;
}

.footer-bottom {
    padding-top: 40px;
    border-top: 1px solid var(--gray-800);
    text-align: center;
    color: var(--gray-400);
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content,
    .about-content,
    .highlight-item,
    .venue-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .speakers-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .cta-content h2 {
        font-size: 2.5rem;
    }

    .cta-stats {
        gap: 40px;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 40px;
    }
}

@media (max-width: 768px) {
    .nav-menu,
    .nav-actions {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .info-cards,
    .speakers-grid,
    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .hero-stats {
        justify-content: space-around;
    }

    .section-title {
        font-size: 2rem;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-stats {
        flex-direction: column;
        gap: 30px;
    }

    .cta-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .schedule-item {
        grid-template-columns: 1fr;
        gap: 20px;
        text-align: center;
    }

    .venue-features {
        grid-template-columns: 1fr;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }

    .hero-title {
        font-size: 1.75rem;
    }

    .info-card,
    .speaker-info {
        padding: 24px 20px;
    }

    .tab-btn {
        padding: 10px 20px;
        font-size: 14px;
    }

    .sponsor-logos {
        gap: 20px;
    }

    .sponsor-logo {
        padding: 15px 20px;
    }
}
