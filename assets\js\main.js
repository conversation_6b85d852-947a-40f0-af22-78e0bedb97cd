/**
 * WordCamp Bhopal 2025 - Modern JavaScript
 * Progressive Enhancement & Interactive Features
 */

(function() {
    'use strict';

    // ===== UTILITY FUNCTIONS =====
    
    /**
     * Debounce function to limit function calls
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Throttle function to limit function calls
     */
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Check if element is in viewport
     */
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    /**
     * Smooth scroll to element
     */
    function smoothScrollTo(element) {
        const headerHeight = document.querySelector('.header').offsetHeight;
        const elementPosition = element.offsetTop - headerHeight - 20;
        
        window.scrollTo({
            top: elementPosition,
            behavior: 'smooth'
        });
    }

    // ===== MOBILE NAVIGATION =====
    
    class MobileNavigation {
        constructor() {
            this.navbar = document.querySelector('.navbar');
            this.toggle = document.querySelector('.navbar-toggle');
            this.menu = document.querySelector('.navbar-menu');
            this.navLinks = document.querySelectorAll('.nav-link');
            this.isOpen = false;
            
            this.init();
        }

        init() {
            if (!this.toggle || !this.menu) return;
            
            this.toggle.addEventListener('click', () => this.toggleMenu());
            
            // Close menu when clicking nav links
            this.navLinks.forEach(link => {
                link.addEventListener('click', () => this.closeMenu());
            });
            
            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!this.navbar.contains(e.target) && this.isOpen) {
                    this.closeMenu();
                }
            });
            
            // Close menu on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isOpen) {
                    this.closeMenu();
                }
            });
        }

        toggleMenu() {
            this.isOpen ? this.closeMenu() : this.openMenu();
        }

        openMenu() {
            this.menu.classList.add('active');
            this.toggle.setAttribute('aria-expanded', 'true');
            this.isOpen = true;
            
            // Animate hamburger lines
            const lines = this.toggle.querySelectorAll('.hamburger-line');
            lines[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
            lines[1].style.opacity = '0';
            lines[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
        }

        closeMenu() {
            this.menu.classList.remove('active');
            this.toggle.setAttribute('aria-expanded', 'false');
            this.isOpen = false;
            
            // Reset hamburger lines
            const lines = this.toggle.querySelectorAll('.hamburger-line');
            lines[0].style.transform = 'none';
            lines[1].style.opacity = '1';
            lines[2].style.transform = 'none';
        }
    }

    // ===== SMOOTH SCROLLING =====
    
    class SmoothScrolling {
        constructor() {
            this.init();
        }

        init() {
            // Handle anchor links
            document.addEventListener('click', (e) => {
                const link = e.target.closest('a[href^="#"]');
                if (!link) return;
                
                const href = link.getAttribute('href');
                if (href === '#') return;
                
                const target = document.querySelector(href);
                if (!target) return;
                
                e.preventDefault();
                smoothScrollTo(target);
                
                // Update URL without jumping
                history.pushState(null, null, href);
            });
        }
    }

    // ===== HEADER SCROLL EFFECTS =====
    
    class HeaderScrollEffects {
        constructor() {
            this.header = document.querySelector('.header');
            this.lastScrollY = window.scrollY;
            this.ticking = false;
            
            this.init();
        }

        init() {
            if (!this.header) return;
            
            window.addEventListener('scroll', () => {
                if (!this.ticking) {
                    requestAnimationFrame(() => this.updateHeader());
                    this.ticking = true;
                }
            });
        }

        updateHeader() {
            const currentScrollY = window.scrollY;
            
            // Add/remove scrolled class
            if (currentScrollY > 50) {
                this.header.classList.add('scrolled');
            } else {
                this.header.classList.remove('scrolled');
            }
            
            // Hide/show header on scroll
            if (currentScrollY > this.lastScrollY && currentScrollY > 100) {
                this.header.style.transform = 'translateY(-100%)';
            } else {
                this.header.style.transform = 'translateY(0)';
            }
            
            this.lastScrollY = currentScrollY;
            this.ticking = false;
        }
    }

    // ===== SCROLL ANIMATIONS =====
    
    class ScrollAnimations {
        constructor() {
            this.animatedElements = document.querySelectorAll('.stat-item, .feature-item, .hero-visual');
            this.observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };
            
            this.init();
        }

        init() {
            if (!('IntersectionObserver' in window)) {
                // Fallback for older browsers
                this.animatedElements.forEach(el => el.classList.add('animate-in'));
                return;
            }
            
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                        this.observer.unobserve(entry.target);
                    }
                });
            }, this.observerOptions);
            
            this.animatedElements.forEach(el => {
                el.classList.add('animate-ready');
                this.observer.observe(el);
            });
        }
    }

    // ===== PERFORMANCE OPTIMIZATIONS =====
    
    class PerformanceOptimizations {
        constructor() {
            this.init();
        }

        init() {
            // Lazy load images
            this.lazyLoadImages();
            
            // Preload critical resources
            this.preloadCriticalResources();
            
            // Optimize scroll performance
            this.optimizeScrollPerformance();
        }

        lazyLoadImages() {
            if ('loading' in HTMLImageElement.prototype) {
                // Native lazy loading support
                const images = document.querySelectorAll('img[data-src]');
                images.forEach(img => {
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                });
            } else {
                // Fallback for older browsers
                const imageObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            imageObserver.unobserve(img);
                        }
                    });
                });
                
                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            }
        }

        preloadCriticalResources() {
            // Preload hero image
            const heroImage = document.querySelector('.hero-image');
            if (heroImage && heroImage.dataset.src) {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'image';
                link.href = heroImage.dataset.src;
                document.head.appendChild(link);
            }
        }

        optimizeScrollPerformance() {
            // Use passive event listeners for better scroll performance
            let supportsPassive = false;
            try {
                const opts = Object.defineProperty({}, 'passive', {
                    get: function() {
                        supportsPassive = true;
                    }
                });
                window.addEventListener('testPassive', null, opts);
                window.removeEventListener('testPassive', null, opts);
            } catch (e) {}

            const passiveOption = supportsPassive ? { passive: true } : false;
            
            // Apply passive listeners to scroll events
            window.addEventListener('scroll', () => {}, passiveOption);
            window.addEventListener('touchstart', () => {}, passiveOption);
            window.addEventListener('touchmove', () => {}, passiveOption);
        }
    }

    // ===== SCHEDULE TABS =====

    class ScheduleTabs {
        constructor() {
            this.tabs = document.querySelectorAll('.schedule-tab');
            this.days = document.querySelectorAll('.schedule-day');

            this.init();
        }

        init() {
            if (!this.tabs.length || !this.days.length) return;

            this.tabs.forEach(tab => {
                tab.addEventListener('click', (e) => this.switchTab(e));
                tab.addEventListener('keydown', (e) => this.handleKeydown(e));
            });
        }

        switchTab(e) {
            const clickedTab = e.target;
            const targetDay = clickedTab.dataset.day;

            // Remove active class from all tabs and days
            this.tabs.forEach(tab => tab.classList.remove('active'));
            this.days.forEach(day => day.classList.remove('active'));

            // Add active class to clicked tab and corresponding day
            clickedTab.classList.add('active');
            const targetDayElement = document.getElementById(targetDay);
            if (targetDayElement) {
                targetDayElement.classList.add('active');
            }

            // Update ARIA attributes
            this.tabs.forEach(tab => tab.setAttribute('aria-selected', 'false'));
            clickedTab.setAttribute('aria-selected', 'true');
        }

        handleKeydown(e) {
            const currentIndex = Array.from(this.tabs).indexOf(e.target);
            let nextIndex;

            switch (e.key) {
                case 'ArrowLeft':
                    nextIndex = currentIndex > 0 ? currentIndex - 1 : this.tabs.length - 1;
                    break;
                case 'ArrowRight':
                    nextIndex = currentIndex < this.tabs.length - 1 ? currentIndex + 1 : 0;
                    break;
                case 'Home':
                    nextIndex = 0;
                    break;
                case 'End':
                    nextIndex = this.tabs.length - 1;
                    break;
                default:
                    return;
            }

            e.preventDefault();
            this.tabs[nextIndex].focus();
            this.tabs[nextIndex].click();
        }
    }

    // ===== ACCESSIBILITY ENHANCEMENTS =====

    class AccessibilityEnhancements {
        constructor() {
            this.init();
        }

        init() {
            this.handleKeyboardNavigation();
            this.improveScreenReaderExperience();
            this.addSkipLinks();
        }

        handleKeyboardNavigation() {
            // Trap focus in mobile menu when open
            const mobileMenu = document.querySelector('.navbar-menu');
            const focusableElements = 'a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select';
            
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Tab' && mobileMenu.classList.contains('active')) {
                    const focusableContent = mobileMenu.querySelectorAll(focusableElements);
                    const firstFocusableElement = focusableContent[0];
                    const lastFocusableElement = focusableContent[focusableContent.length - 1];

                    if (e.shiftKey) {
                        if (document.activeElement === firstFocusableElement) {
                            lastFocusableElement.focus();
                            e.preventDefault();
                        }
                    } else {
                        if (document.activeElement === lastFocusableElement) {
                            firstFocusableElement.focus();
                            e.preventDefault();
                        }
                    }
                }
            });
        }

        improveScreenReaderExperience() {
            // Add live region for dynamic content updates
            const liveRegion = document.createElement('div');
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.className = 'sr-only';
            document.body.appendChild(liveRegion);
            
            // Announce page changes
            window.addEventListener('popstate', () => {
                liveRegion.textContent = 'Page content updated';
            });
        }

        addSkipLinks() {
            // Skip links are already in HTML, just ensure they work properly
            const skipLink = document.querySelector('.skip-link');
            if (skipLink) {
                skipLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    const target = document.querySelector(skipLink.getAttribute('href'));
                    if (target) {
                        target.focus();
                        target.scrollIntoView();
                    }
                });
            }
        }
    }

    // ===== INITIALIZATION =====
    
    function init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeApp);
        } else {
            initializeApp();
        }
    }

    function initializeApp() {
        // Initialize all components
        new MobileNavigation();
        new SmoothScrolling();
        new HeaderScrollEffects();
        new ScrollAnimations();
        new ScheduleTabs();
        new PerformanceOptimizations();
        new AccessibilityEnhancements();

        // Add loaded class to body for CSS animations
        document.body.classList.add('loaded');

        console.log('WordCamp Bhopal 2025 - Website initialized successfully! 🚀');
    }

    // Start the application
    init();

})();
