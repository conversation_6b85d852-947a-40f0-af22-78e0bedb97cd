/* WordCamp Bhopal 2025 - Modern CSS Framework */

/* ===== CSS CUSTOM PROPERTIES (DESIGN TOKENS) ===== */
:root {
  /* Colors - Matching Original WordCamp Bhopal 2025 */
  --color-primary: #fbed6d; /* Warm yellow from original */
  --color-primary-dark: #f4d03f;
  --color-primary-light: #fff9e6;
  --color-secondary: #043f32; /* Deep green from original */
  --color-secondary-dark: #032d24;
  --color-secondary-light: #065a47;

  /* Bhopal Natural Colors - City of Lakes Theme */
  --color-accent: #6880ff; /* Tertiary blue from original */
  --color-accent-light: #8fa4ff;
  --color-lake-blue: #4a90e2; /* Natural lake blue */
  --color-heritage-green: #2d5a3d; /* Deep forest green */
  --color-earth-brown: #8b4513; /* Natural earth tones */
  --color-sunset-orange: #ff7f50; /* Bhopal sunset */

  /* Natural Neutral Colors */
  --color-white: #ffffff;
  --color-cream: #f5f1e8; /* Background from original */
  --color-warm-gray-50: #faf9f7;
  --color-warm-gray-100: #f3f1ed;
  --color-warm-gray-200: #e8e4de;
  --color-warm-gray-300: #d6d0c4;
  --color-warm-gray-400: #b8b0a0;
  --color-warm-gray-500: #9a8f7e;
  --color-warm-gray-600: #7c6f5d;
  --color-warm-gray-700: #5d523f;
  --color-warm-gray-800: #4d3a14; /* Contrast from original */
  --color-warm-gray-900: #3a2a0f;
  
  /* Typography - Matching Original */
  --font-family-primary: 'Roboto Slab', serif; /* Body font from original */
  --font-family-heading: 'Sora', sans-serif; /* Heading font from original */
  --font-family-accent: 'DM Sans', sans-serif; /* Accent font from original */
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  
  /* Font Sizes - Fluid Typography */
  --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --font-size-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --font-size-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --font-size-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --font-size-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --font-size-2xl: clamp(1.5rem, 1.3rem + 1vw, 2rem);
  --font-size-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem);
  --font-size-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  --font-size-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);
  
  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* Container Sizes */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ===== RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

/* Custom Scrollbar - Trending Design */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-warm-gray-100);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--color-secondary) 0%, var(--color-primary) 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--color-secondary-dark) 0%, var(--color-primary-dark) 100%);
}

/* Firefox scrollbar */
html {
  scrollbar-width: thin;
  scrollbar-color: var(--color-secondary) var(--color-warm-gray-100);
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-warm-gray-800);
  background-color: var(--color-cream);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern Selection Styling */
::selection {
  background-color: var(--color-primary);
  color: var(--color-warm-gray-800);
}

::-moz-selection {
  background-color: var(--color-primary);
  color: var(--color-warm-gray-800);
}

/* Focus Visible for Better Accessibility */
:focus-visible {
  outline: 2px solid var(--color-secondary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-warm-gray-900);
  margin-bottom: var(--space-4);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
  margin-bottom: var(--space-4);
  line-height: var(--line-height-relaxed);
}

.lead {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
}

/* ===== LAYOUT UTILITIES ===== */
.container {
  width: 100%;
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 640px) {
  .container { padding: 0 var(--space-6); }
}

@media (min-width: 1024px) {
  .container { padding: 0 var(--space-8); }
}

.section {
  padding: var(--space-16) 0;
}

.section-alt {
  background-color: var(--color-warm-gray-50);
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.section-title {
  margin-bottom: var(--space-4);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  max-width: 600px;
  margin: 0 auto;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.btn-primary {
  background-color: var(--color-secondary);
  color: var(--color-white);
  border-color: var(--color-secondary);
  border-radius: 100px;
}

.btn-primary:hover {
  background-color: var(--color-secondary-dark);
  border-color: var(--color-secondary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background-color: var(--color-primary);
  color: var(--color-warm-gray-800);
  border-color: var(--color-primary);
  border-radius: 100px;
}

.btn-secondary:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-secondary);
  border-color: var(--color-secondary);
  border-radius: 100px;
}

.btn-outline:hover {
  background-color: var(--color-secondary);
  color: var(--color-white);
}

.btn-large {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-lg);
}

/* ===== ACCESSIBILITY ===== */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: var(--color-white);
  padding: var(--space-2) var(--space-4);
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: var(--z-tooltip);
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/* ===== HEADER & NAVIGATION ===== */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(245, 241, 232, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-warm-gray-200);
  z-index: var(--z-fixed);
  transition: all var(--transition-normal);
}

.navbar {
  padding: var(--space-4) 0;
}

.navbar .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar-brand .logo img {
  height: 40px;
  width: auto;
}

.navbar-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-2);
}

.hamburger-line {
  width: 24px;
  height: 2px;
  background-color: var(--color-gray-800);
  margin: 2px 0;
  transition: var(--transition-fast);
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.navbar-nav {
  display: flex;
  list-style: none;
  gap: var(--space-6);
  margin: 0;
}

.nav-link {
  color: var(--color-warm-gray-700);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
  position: relative;
  font-family: var(--font-family-accent);
}

.nav-link:hover {
  color: var(--color-secondary);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-secondary);
  transition: width var(--transition-fast);
}

.nav-link:hover::after {
  width: 100%;
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .navbar-toggle {
    display: flex;
  }
  
  .navbar-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--color-white);
    border-top: 1px solid var(--color-gray-200);
    flex-direction: column;
    padding: var(--space-6);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }
  
  .navbar-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
  
  .navbar-nav {
    flex-direction: column;
    width: 100%;
    gap: var(--space-4);
  }
  
  .navbar-cta {
    margin-top: var(--space-4);
  }
}

/* ===== HERO SECTION ===== */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-heritage-green) 50%, var(--color-lake-blue) 100%);
}

.hero-background {
  position: absolute;
  inset: 0;
  z-index: 1;
}

.hero-pattern {
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
  animation: float 20s ease-in-out infinite;
}

.hero-gradient {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, rgba(4, 63, 50, 0.8) 0%, rgba(45, 90, 61, 0.7) 50%, rgba(74, 144, 226, 0.6) 100%);
}

.hero .container {
  position: relative;
  z-index: 2;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-12);
  align-items: center;
  min-height: 80vh;
}

@media (min-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr 1fr;
  }
}

.hero-text {
  text-align: center;
}

@media (min-width: 1024px) {
  .hero-text {
    text-align: left;
  }
}

.hero-title {
  margin-bottom: var(--space-6);
}

.hero-title-main {
  display: block;
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--color-white);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-title-year {
  display: block;
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
}

.hero-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

@media (min-width: 640px) {
  .hero-details {
    flex-direction: row;
    justify-content: center;
  }
  
  @media (min-width: 1024px) {
    .hero-details {
      justify-content: flex-start;
    }
  }
}

.hero-detail-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: rgba(255, 255, 255, 0.9);
  font-weight: var(--font-weight-medium);
}

.detail-icon {
  font-size: var(--font-size-lg);
}

.hero-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

@media (min-width: 640px) {
  .hero-actions {
    flex-direction: row;
    justify-content: center;
  }
  
  @media (min-width: 1024px) {
    .hero-actions {
      justify-content: flex-start;
    }
  }
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-image-container {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.hero-image {
  width: 100%;
  height: auto;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}

.scroll-indicator {
  position: absolute;
  bottom: var(--space-8);
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.scroll-arrow {
  width: 24px;
  height: 24px;
  border-right: 2px solid rgba(255, 255, 255, 0.7);
  border-bottom: 2px solid rgba(255, 255, 255, 0.7);
  transform: rotate(45deg);
  animation: bounce 2s infinite;
}

/* ===== ANIMATIONS ===== */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0) rotate(45deg); }
  40% { transform: translateY(-10px) rotate(45deg); }
  60% { transform: translateY(-5px) rotate(45deg); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Scroll Animation Classes */
.animate-ready {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.stat-item.animate-ready {
  transform: scale(0.9);
}

.stat-item.animate-in {
  transform: scale(1);
}

.hero-visual.animate-ready {
  transform: translateX(30px);
}

.hero-visual.animate-in {
  transform: translateX(0);
}

/* Header Scroll Effects */
.header.scrolled {
  background-color: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-md);
}

.header {
  transition: all var(--transition-normal);
}

/* Loading States */
body:not(.loaded) .hero-content {
  opacity: 0;
}

body.loaded .hero-content {
  opacity: 1;
  animation: fadeInUp 0.8s ease-out;
}

body.loaded .hero-visual {
  animation: fadeInRight 0.8s ease-out 0.2s both;
}

/* Staggered Animations */
.stat-item:nth-child(1) { transition-delay: 0.1s; }
.stat-item:nth-child(2) { transition-delay: 0.2s; }
.stat-item:nth-child(3) { transition-delay: 0.3s; }
.stat-item:nth-child(4) { transition-delay: 0.4s; }

.feature-item:nth-child(1) { transition-delay: 0.1s; }
.feature-item:nth-child(2) { transition-delay: 0.2s; }
.feature-item:nth-child(3) { transition-delay: 0.3s; }
.feature-item:nth-child(4) { transition-delay: 0.4s; }

/* ===== SPEAKERS SECTION ===== */
.speakers-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
}

@media (min-width: 768px) {
  .speakers-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .speakers-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.speaker-card {
  background-color: var(--color-white);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  text-align: center;
}

.speaker-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.speaker-image {
  width: 120px;
  height: 120px;
  margin: 0 auto var(--space-4);
  border-radius: var(--radius-full);
  overflow: hidden;
  background-color: var(--color-gray-200);
  display: flex;
  align-items: center;
  justify-content: center;
}

.speaker-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.speaker-name {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-2);
  color: var(--color-gray-900);
}

.speaker-title {
  font-size: var(--font-size-base);
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-4);
}

.speaker-bio {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

.speakers-cta {
  text-align: center;
  margin-top: var(--space-12);
}

/* ===== SCHEDULE SECTION ===== */
.schedule-tabs {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

.schedule-tab {
  padding: var(--space-3) var(--space-6);
  background-color: var(--color-white);
  border: 2px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.schedule-tab:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.schedule-tab.active {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-white);
}

.schedule-day {
  display: none;
}

.schedule-day.active {
  display: block;
}

.schedule-item {
  display: grid;
  grid-template-columns: 150px 1fr;
  gap: var(--space-6);
  padding: var(--space-6);
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.schedule-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateX(4px);
}

@media (max-width: 767px) {
  .schedule-item {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
}

.schedule-time {
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  font-size: var(--font-size-lg);
}

.schedule-title {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-2);
  color: var(--color-gray-900);
}

.schedule-description {
  color: var(--color-gray-600);
  margin-bottom: var(--space-2);
  font-size: var(--font-size-sm);
}

.schedule-speaker {
  font-size: var(--font-size-sm);
  color: var(--color-secondary);
  font-weight: var(--font-weight-medium);
  font-style: italic;
}

/* ===== SPONSORS SECTION ===== */
.sponsors-tiers {
  margin-bottom: var(--space-12);
}

.sponsor-tier {
  margin-bottom: var(--space-12);
}

.tier-title {
  text-align: center;
  font-size: var(--font-size-2xl);
  color: var(--color-gray-800);
  margin-bottom: var(--space-8);
  position: relative;
}

.tier-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
}

.sponsors-grid {
  display: grid;
  gap: var(--space-6);
  justify-items: center;
}

.sponsors-grid.platinum {
  grid-template-columns: 1fr;
  max-width: 400px;
  margin: 0 auto;
}

.sponsors-grid.gold {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  max-width: 800px;
  margin: 0 auto;
}

.sponsors-grid.silver {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.sponsor-card {
  background-color: var(--color-white);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  text-align: center;
  width: 100%;
  max-width: 350px;
}

.sponsor-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.sponsor-logo {
  width: 120px;
  height: 120px;
  margin: 0 auto var(--space-4);
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--color-gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--color-gray-200);
}

.platinum .sponsor-logo {
  width: 150px;
  height: 150px;
  border-color: var(--color-accent);
}

.gold .sponsor-logo {
  width: 130px;
  height: 130px;
  border-color: var(--color-accent-light);
}

.sponsor-logo img {
  width: 80%;
  height: 80%;
  object-fit: contain;
}

.sponsor-name {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-3);
  color: var(--color-gray-800);
}

.sponsor-description {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin-bottom: 0;
}

.sponsors-cta {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  justify-content: center;
}

@media (min-width: 640px) {
  .sponsors-cta {
    flex-direction: row;
  }
}

/* ===== ABOUT SECTION ===== */
.about-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-12);
  align-items: center;
}

@media (min-width: 1024px) {
  .about-content {
    grid-template-columns: 2fr 1fr;
  }
}

/* ===== DISCOVER BHOPAL SECTION ===== */
.discover-bhopal {
  background: linear-gradient(135deg, var(--color-warm-gray-50) 0%, var(--color-primary-light) 100%);
  position: relative;
  overflow: hidden;
}

.discover-bhopal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(104, 128, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(251, 237, 109, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(4, 63, 50, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.bhopal-highlights {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
  position: relative;
  z-index: 1;
}

@media (min-width: 640px) {
  .bhopal-highlights {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .bhopal-highlights {
    grid-template-columns: repeat(4, 1fr);
  }
}

.highlight-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.highlight-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-accent) 50%, var(--color-secondary) 100%);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.highlight-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.highlight-card:hover::before {
  transform: scaleX(1);
}

.highlight-icon {
  font-size: 3rem;
  margin-bottom: var(--space-4);
  display: block;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.highlight-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-secondary);
  margin-bottom: var(--space-3);
  font-family: var(--font-family-heading);
}

.highlight-description {
  font-size: var(--font-size-sm);
  color: var(--color-warm-gray-700);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-6);
}

@media (min-width: 640px) {
  .about-stats {
    grid-template-columns: repeat(4, 1fr);
  }
  
  @media (min-width: 1024px) {
    .about-stats {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

.stat-item {
  text-align: center;
  padding: var(--space-6);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(251, 237, 109, 0.1) 0%, rgba(104, 128, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.stat-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-item:hover::before {
  opacity: 1;
}

.stat-number {
  display: block;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--color-secondary);
  margin-bottom: var(--space-2);
  position: relative;
  z-index: 1;
}

.stat-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* ===== VENUE SECTION ===== */
.venue-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-12);
  align-items: start;
}

@media (min-width: 1024px) {
  .venue-content {
    grid-template-columns: 1fr 1fr;
  }
}

.venue-name {
  font-size: var(--font-size-2xl);
  color: var(--color-primary);
  margin-bottom: var(--space-4);
}

.venue-address {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  margin-bottom: var(--space-6);
  line-height: var(--line-height-relaxed);
}

.venue-features {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

@media (min-width: 640px) {
  .venue-features {
    grid-template-columns: repeat(2, 1fr);
  }
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.feature-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.feature-text {
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
}

.venue-map {
  background-color: var(--color-gray-100);
  border-radius: var(--radius-xl);
  overflow: hidden;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-placeholder {
  text-align: center;
  color: var(--color-gray-500);
  font-weight: var(--font-weight-medium);
}

/* ===== CTA SECTION ===== */
.cta {
  background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-heritage-green) 50%, var(--color-lake-blue) 100%);
  color: var(--color-white);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(251, 237, 109, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
  animation: float 20s ease-in-out infinite;
}

.cta-title {
  color: var(--color-white);
  margin-bottom: var(--space-4);
}

.cta-text {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.9;
}

.cta-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  justify-content: center;
}

@media (min-width: 640px) {
  .cta-actions {
    flex-direction: row;
  }
}

.cta .btn-primary {
  background-color: var(--color-white);
  color: var(--color-secondary);
  border-color: var(--color-white);
}

.cta .btn-primary:hover {
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-100);
}

.cta .btn-secondary {
  background-color: transparent;
  color: var(--color-white);
  border-color: var(--color-white);
}

.cta .btn-secondary:hover {
  background-color: var(--color-white);
  color: var(--color-secondary);
}

/* ===== FOOTER ===== */
.footer {
  background-color: var(--color-warm-gray-900);
  color: var(--color-warm-gray-300);
  padding: var(--space-16) 0 var(--space-8);
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

@media (min-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr 2fr 1fr;
    gap: var(--space-12);
  }
}

.footer-logo {
  height: 40px;
  width: auto;
  margin-bottom: var(--space-4);
  filter: brightness(0) invert(1);
}

.footer-tagline {
  color: var(--color-gray-400);
  margin-bottom: 0;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-8);
}

.footer-title {
  color: var(--color-white);
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-4);
}

.footer-nav {
  list-style: none;
}

.footer-nav li {
  margin-bottom: var(--space-2);
}

.footer-nav a {
  color: var(--color-gray-400);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-nav a:hover {
  color: var(--color-white);
}

.social-links {
  display: flex;
  gap: var(--space-4);
  margin-top: var(--space-4);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--color-gray-800);
  color: var(--color-gray-400);
  border-radius: var(--radius-lg);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.social-link:hover {
  background-color: var(--color-primary);
  color: var(--color-white);
  transform: translateY(-2px);
}

.social-icon {
  width: 20px;
  height: 20px;
}

.footer-bottom {
  border-top: 1px solid var(--color-gray-800);
  padding-top: var(--space-8);
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  text-align: center;
}

@media (min-width: 768px) {
  .footer-bottom {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
}

.footer-copyright,
.footer-wordpress {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
  margin: 0;
}

.footer-copyright a,
.footer-wordpress a {
  color: var(--color-gray-400);
  text-decoration: none;
}

.footer-copyright a:hover,
.footer-wordpress a:hover {
  color: var(--color-white);
}

/* ===== RESPONSIVE UTILITIES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none; }
.visible { display: block; }

@media (max-width: 767px) {
  .hidden-mobile { display: none; }
  .visible-mobile { display: block; }
}

@media (min-width: 768px) {
  .hidden-desktop { display: none; }
  .visible-desktop { display: block; }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* Critical CSS - Above the fold styles */
.hero,
.header,
.navbar {
  contain: layout style paint;
}

/* Optimize animations for performance */
.stat-item,
.feature-item,
.speaker-card,
.sponsor-card {
  will-change: transform;
}

.stat-item:hover,
.feature-item:hover,
.speaker-card:hover,
.sponsor-card:hover {
  will-change: auto;
}

/* Optimize images */
img {
  max-width: 100%;
  height: auto;
  loading: lazy;
}

/* Critical images should load eagerly */
.hero-image,
.logo img {
  loading: eager;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .hero-pattern {
    animation: none;
  }

  .scroll-arrow {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn {
    border-width: 3px;
  }

  .nav-link::after {
    height: 3px;
  }

  .feature-item,
  .stat-item,
  .speaker-card,
  .sponsor-card {
    border: 2px solid var(--color-gray-400);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --color-white: #0f172a;
    --color-gray-50: #1e293b;
    --color-gray-100: #334155;
    --color-gray-900: #f8fafc;
    --color-gray-800: #e2e8f0;
    --color-gray-700: #cbd5e1;
  }

  .hero {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-lake-blue) 100%);
  }

  .footer-logo {
    filter: brightness(1) invert(0);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .header,
  .footer,
  .btn,
  .navbar-toggle,
  .scroll-indicator {
    display: none !important;
  }

  .hero {
    min-height: auto;
    padding: var(--space-8) 0;
  }

  .section {
    padding: var(--space-8) 0;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  h1 { font-size: 24pt; }
  h2 { font-size: 20pt; }
  h3 { font-size: 16pt; }
  h4 { font-size: 14pt; }
  h5 { font-size: 12pt; }
  h6 { font-size: 12pt; }

  /* Print-specific optimizations */
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  .container {
    max-width: none;
    padding: 0;
  }
}
