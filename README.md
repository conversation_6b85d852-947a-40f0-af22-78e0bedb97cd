# WordCamp Bhopal 2025 - Modern Website

A modern, responsive website for WordCamp Bhopal 2025, built with cutting-edge web technologies and best practices.

## 🚀 Features

### Modern Design
- **Contemporary Layout**: Clean, modern design that reflects WordPress branding
- **Bhopal Cultural Elements**: Color palette inspired by the "City of Lakes"
- **Responsive Design**: Optimized for all devices and screen sizes
- **Accessibility First**: WCAG 2.1 AA compliant with proper semantic markup

### Technical Excellence
- **Modern CSS**: CSS Grid, Flexbox, Custom Properties (CSS Variables)
- **Progressive Enhancement**: Works without JavaScript, enhanced with it
- **Performance Optimized**: Fast loading times with optimized assets
- **SEO Ready**: Proper meta tags, structured data, and semantic HTML

### Interactive Features
- **Smooth Scrolling**: Enhanced navigation experience
- **Mobile Navigation**: Touch-friendly hamburger menu
- **Scroll Animations**: Engaging animations triggered by scroll
- **Schedule Tabs**: Interactive schedule with keyboard navigation
- **Header Effects**: Dynamic header behavior on scroll

## 📁 Project Structure

```
wordcamp-bhopal-2025/
├── index.html                 # Main HTML file
├── assets/
│   ├── css/
│   │   └── styles.css        # Main stylesheet with modern CSS
│   ├── js/
│   │   └── main.js           # JavaScript functionality
│   ├── images/
│   │   ├── wordcamp-bhopal-logo.svg
│   │   ├── wordcamp-hero-illustration.svg
│   │   ├── speaker-placeholder.jpg
│   │   └── sponsor-placeholder.png
│   └── fonts/                # Web fonts (to be added)
└── README.md                 # This file
```

## 🎨 Design System

### Color Palette
- **Primary**: #0073aa (WordPress Blue)
- **Secondary**: #ff6b35 (Vibrant Orange)
- **Accent**: #d4af37 (Golden - Heritage)
- **Lake Blue**: #1e88e5 (City of Lakes)
- **Heritage Green**: #2e7d32

### Typography
- **Primary Font**: Inter (Modern, readable sans-serif)
- **Heading Font**: Poppins (Bold, distinctive headings)
- **Monospace**: JetBrains Mono (Code snippets)

### Spacing Scale
- Uses a consistent 8px base unit
- Fluid spacing that adapts to screen size
- CSS Custom Properties for maintainability

## 🛠️ Technologies Used

### Frontend
- **HTML5**: Semantic markup with accessibility features
- **CSS3**: Modern CSS with Grid, Flexbox, and Custom Properties
- **Vanilla JavaScript**: Progressive enhancement without frameworks
- **SVG**: Scalable vector graphics for logos and illustrations

### Performance
- **Lazy Loading**: Images load as needed
- **Critical CSS**: Above-the-fold styles prioritized
- **Optimized Assets**: Compressed images and minified code
- **Preloading**: Critical resources preloaded for faster rendering

### Accessibility
- **ARIA Labels**: Proper labeling for screen readers
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Visible focus indicators
- **Skip Links**: Quick navigation for assistive technologies

## 📱 Responsive Breakpoints

- **Mobile**: 320px - 767px
- **Tablet**: 768px - 1023px
- **Desktop**: 1024px - 1279px
- **Large Desktop**: 1280px+

## 🚀 Getting Started

1. **Clone or Download** the project files
2. **Open** `index.html` in a web browser
3. **Serve** from a local server for best experience:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```

## 📋 Content Sections

### Implemented Sections
- ✅ **Hero Section**: Eye-catching introduction with event details
- ✅ **About**: Information about WordCamp Bhopal 2025
- ✅ **Venue**: Location details and features
- ✅ **Speakers**: Featured speakers (placeholder content)
- ✅ **Schedule**: Two-day event schedule with tabs
- ✅ **Sponsors**: Sponsor tiers and information
- ✅ **Footer**: Links, social media, and contact info

### Future Enhancements
- 🔄 **Tickets**: Registration and pricing information
- 🔄 **Blog**: Latest news and updates
- 🔄 **Gallery**: Photos from previous events
- 🔄 **Contact**: Contact form and organizer information

## 🎯 Performance Metrics

The website is optimized for:
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **First Input Delay**: < 100ms

## 🌐 Browser Support

- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Progressive Enhancement**: Graceful degradation for older browsers
- **Mobile Browsers**: iOS Safari 14+, Chrome Mobile 90+

## 📞 Support

For questions about this website implementation:
- **WordPress Community**: [WordPress.org](https://wordpress.org)
- **WordCamp Central**: [central.wordcamp.org](https://central.wordcamp.org)
- **Local Community**: WordCamp Bhopal Organizing Team

## 📄 License

This project is open source and available under the [GPL v2 License](https://www.gnu.org/licenses/gpl-2.0.html), 
consistent with WordPress licensing.

---

**Built with ❤️ for the WordPress Community in Bhopal**

*WordCamp Bhopal 2025 - December 20-21, 2025*
